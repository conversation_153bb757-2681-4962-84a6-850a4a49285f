{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "cYSsmFYrlpuVHFkFQexBs5v1kdv/7ugk/03t8Z8E4Hs=", "__NEXT_PREVIEW_MODE_ID": "4705511867c2a83bba79c1e577c62232", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6ffae69061858c04fd1e337ddd827a32cf5378eb198bbaddb02184a82abbe44f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a9165f373d3d656cb3c1ca2678943b31ee40c6ce6ed9d41ccbab84ea9a2b8b9a"}}}, "sortedMiddleware": ["/"], "functions": {}}