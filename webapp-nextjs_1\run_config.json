{"system": {"name": "CABLYS Next.js Webapp", "version": "2.0.0", "description": "Sistema di gestione cavi avanzato"}, "backend": {"enabled": true, "host": "0.0.0.0", "port": 8001, "reload": true, "workers": 1, "timeout": 60, "relative_path": "../webapp", "module": "backend.main:app"}, "frontend": {"enabled": true, "port": 3000, "turbopack": true, "command": "npm run dev", "timeout": 30}, "database": {"host": "localhost", "port": 5432, "name": "cantieri", "user": "postgres", "check_connection": true}, "development": {"auto_open_browser": true, "show_logs": true, "restart_on_failure": false, "max_restart_attempts": 3}, "monitoring": {"health_check_interval": 30, "log_level": "INFO", "save_logs": false, "log_file": "run_system.log"}}